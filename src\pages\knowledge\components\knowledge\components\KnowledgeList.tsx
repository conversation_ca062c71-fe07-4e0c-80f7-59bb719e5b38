import {
  Button,
  Divider,
  Input,
  Select,
  Grid,
  Typography,
  Layout,
  Tag,
  Modal,
  Space,
  Spin,
  Skeleton,
} from '@arco-design/web-react';
import { IconSearch } from '@arco-design/web-react/icon';
import IconClose from '@/assets/acp/IconClose.svg';
import {
  useState,
  useRef,
  useImperativeHandle,
  useMemo,
  forwardRef,
  useCallback,
} from 'react';
import useLocale from '@/utils/useLocale';
import IconScreen from '@/assets/application/screen.svg';
import KnowledgeCard from './KnowledgeCard';
import { Action } from './KnowledgeCard';
import { useNavigate } from 'react-router-dom';
import {
  useInfiniteFetchKnowledgeList,
  useDeleteKnowledge,
  useUpdateKbStatus,
  useFetchGetKbTags,
  useFetchKnowledgeAssociatedEmployees,
} from '../hooks/knowledge-hooks';
import { formatDate } from '@/utils/date';
import IconEmptyModelConfig from '@/assets/model/IconEmptyModelConfig.svg';
import InfiniteScroll from 'react-infinite-scroll-component';
import { useSetNextDocumentStatus } from '../hooks/document-hooks';
import KnowledgeModal from './KnowledgeModal';

const Option = Select.Option;
const { Row, Col } = Grid;
const { Text } = Typography;
const { Header, Content } = Layout;

const initActionButtons: Action[] = [
  { label: '编辑', className: 'text-[#333333]', type: 'edit' },
  { label: '删除', className: 'text-[#333333]', type: 'del' },
] as const;

// 删除确认弹窗
const KnowledgeDeleteConfirmModal = forwardRef(
  ({ confirmDelete }: { confirmDelete: (data: any) => void }, ref) => {
    const [visible, setVisible] = useState(false);
    const currentModalData = useRef<any>();

    // 获取知识库关联的AI员工列表
    const { employees, loading: employeesLoading } = useFetchKnowledgeAssociatedEmployees(
      currentModalData.current?.id || ''
    );

    const open = (row: any) => {
      currentModalData.current = row;
      setVisible(true);
    };

    const toggleModal = () => {
      setVisible(!visible);
      currentModalData.current = null;
    };

    useImperativeHandle(ref, () => ({
      open: open,
    }));

    const _confirmDelete = () => {
      confirmDelete(currentModalData.current);
      toggleModal();
    };
    return (
      <Modal
        visible={visible}
        onCancel={toggleModal}
        closeIcon={<IconClose />}
        maskClosable={false}
        footer={null}
        simple={true}
        closable={true}
        className="p-[24px] w-[480px] rounded-[16px] [&_.arco-modal-title]"
        title={
          <div className="text-left font-semibold text-[18px] leading-[24px] text-[#333] flex justify-between">
            <span>删除知识库</span>
          </div>
        }
      >
        <div className="flex flex-col">
          <Text className="text-[14px] text-[#5c5c5c] font-[400] mb-4">
            “{currentModalData.current?.name}”内文件将被删除无法恢复
          </Text>

          {/* 显示关联的AI员工列表 */}
          {employees && employees.length > 0 && (
            <div className="mb-4">
              <Text className="text-[14px] text-[#333] font-[500] mb-2 block">
                以下AI员工正在使用该知识库：
              </Text>
              <div className="max-h-[200px] overflow-y-auto bg-[#f8f9fa] rounded-[8px] p-3">
                {employeesLoading ? (
                  <div className="flex justify-center items-center py-4">
                    <Spin size={14} />
                  </div>
                ) : (
                  <div className="space-y-2">
                    {employees.map((employee: any, index: number) => (
                      <div key={employee.id || index} className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-[#666] rounded-full flex-shrink-0"></div>
                        <Text className="text-[14px] text-[#333]">
                          {employee.name || `AI员工${index + 1}`}
                        </Text>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
        <div className="mt-[24px] flex justify-end gap-[8px]">
          <Space>
            <Button
              className="flex justify-center items-center p-[18px_24px] rounded-[8px] font-[600] text-[14px] bg-white text-[#5c5c5c] border border-[#ebebeb] hover:bg-[#fafafa]"
              onClick={toggleModal}
            >
              取消
            </Button>
            <Button
              className="flex justify-center items-center p-[18px_24px] rounded-[8px] font-[600] text-[14px] bg-[#d54941] text-white hover:bg-[#cd463e]"
              type="primary"
              status="danger"
              onClick={_confirmDelete}
            >
              删除
            </Button>
          </Space>
        </div>
      </Modal>
    );
  }
);
// 知识库列表
export default function KnowledgeList() {
  const locale = useLocale();
  const navigate = useNavigate();

  const {
    fetchNextPage,
    data,
    hasNextPage,
    searchString,
    handleInputChange,
    loading,
    refetch,
    tags,
    setTags,
    role,
    setRole,
  } = useInfiniteFetchKnowledgeList();

  const { kbTags } = useFetchGetKbTags();

  const { updateKnowledgeStatus } = useUpdateKbStatus();

  const { setDocumentStatus } = useSetNextDocumentStatus();

  const nextList = useMemo(() => {
    const list =
      data?.pages?.flatMap((x) => (Array.isArray(x.kbs) ? x.kbs : [])) ?? [];
    return list;
  }, [data?.pages]);

  const total = useMemo(() => {
    return data?.pages.at(-1).total ?? 0;
  }, [data?.pages]);

  const knowledgeModalRef = useRef<{
    open: (form?: any) => void;
  }>();

  const knowledgeDeleteConfirmModalRef = useRef<{
    open: (form?: any) => void;
  }>();

  const handleAction = (row: any, type: Action['type']) => {
    switch (type) {
      // 启动
      case 'enable':
        handleSwitch(row, true);
        break;
      case 'disable':
        handleSwitch(row, false);
        break;
      case 'edit':
        knowledgeModalRef.current?.open(row);
        break;
      case 'del':
        knowledgeDeleteConfirmModalRef.current?.open(row);
        break;
    }
  };

  const { deleteKnowledge } = useDeleteKnowledge();

  const confirmDelete = (data) => {
    deleteKnowledge(data.id);
  };

  const getActionButtons = useCallback(
    (row: any) => {
      if (row.status === '1') {
        const disableActionButtons: Action[] = [
          { label: '禁用', className: 'text-[#d54941]', type: 'disable' },
        ];
        return disableActionButtons.concat(initActionButtons);
      }
      const startActionButtons: Action[] = [
        { label: '启动', className: 'text-[#2ba471]', type: 'enable' },
      ];
      return startActionButtons.concat(initActionButtons);
    },
    [initActionButtons]
  );

  const handleSwitch = (row: any, value: boolean) => {
    updateKnowledgeStatus({
      kb_id: row.id,
      status: value ? '1' : '0',
    });
  };

  const handleToDataset = (row: any) => {
    navigate(`/knowledge/dataset?id=${row.id}`);
  };

  return (
    <>
      <Header className="flex justify-between items-center">
        <Button
          className="h-[40px] p-[8px_24px] rounded-[8px]"
          size="large"
          type="primary"
          onClick={() => knowledgeModalRef.current?.open()}
        >
          创建库
        </Button>
        <Row align="center" gutter={8}>
          <Col flex="auto">
            <Text className="text-[#adadad]">共 {total} 个知识库</Text>
          </Col>
          <Col flex="240px">
            <Input
              className={`[&_.arco-input-inner-wrapper]:bg-white [&_.arco-input-inner-wrapper]:border-[#ebebeb] [&_.arco-input-inner-wrapper]:rounded-[8px] h-[40px] [&_.arco-input-inner-wrapper]:placeholder:text-[#d6d6d6]`}
              prefix={<IconSearch />}
              placeholder="AI搜索..."
              allowClear
              onChange={(v, e) => handleInputChange(e)}
            />
          </Col>
          <Col flex="180px">
            <Select
              className="w-full [&_.arco-select-view]:text-[#5c5c5c] [&_.arco-select-view]:bg-white [&_.arco-select-view]:border-[#ebebeb] [&_.arco-select-view]:rounded-[8px] [&_.arco-input-tag-view]:h-[40px] [&_.arco-input-tag-view]:leading-[40px]"
              placeholder="标签"
              prefix={<IconScreen />}
              allowClear={true}
              mode="multiple"
              maxTagCount={1}
              triggerProps={{
                autoAlignPopupWidth: false,
                autoAlignPopupMinWidth: true,
                position: 'bl',
              }}
              value={tags}
              onChange={(v) => {
                setTags(v);
              }}
            >
              {kbTags?.map((tag, tagIndex) => (
                <Option key={tagIndex} value={tag}>
                  {tag}
                </Option>
              ))}
            </Select>
          </Col>
          <Col flex="160px">
            <Select
              className="w-full [&_.arco-select-view]:text-[#5c5c5c] [&_.arco-select-view]:bg-white [&_.arco-select-view]:border-[#ebebeb] [&_.arco-select-view]:rounded-[8px] [&_.arco-select-view]:h-[40px] [&_.arco-select-view]:leading-[40px]"
              placeholder="选择角色"
              allowClear={true}
              triggerProps={{
                autoAlignPopupWidth: false,
                autoAlignPopupMinWidth: true,
                position: 'bl',
              }}
              value={role}
              onChange={(v) => {
                setRole(v);
              }}
            >
              <Option value={'all'}>全部</Option>
              <Option value={'admin'}>管理员</Option>
              <Option value={'user'}>用户</Option>
            </Select>
          </Col>
        </Row>
      </Header>
      <Divider />
      <Content className="basis-0 overflow-auto" id="scrollableDiv">
        <Spin
          className="block h-full relative [&_.arco-spin-children]:h-full"
          loading={loading}
        >
          <InfiniteScroll
            dataLength={nextList?.length ?? 0}
            next={fetchNextPage}
            hasMore={hasNextPage}
            loader={
              <Skeleton
                className="mt-5"
                image={{ shape: 'circle' }}
                text={{
                  rows: 2,
                }}
              />
            }
            endMessage={!!total && <Divider>没有更多数据了 🤐</Divider>}
            scrollableTarget="scrollableDiv"
            scrollThreshold="200px"
            style={{
              overflow: 'unset',
            }}
          >
            <div className="grid grid-cols-4 gap-4">
              {nextList?.length > 0 ? (
                nextList.map((item, index) => {
                  return (
                    <KnowledgeCard
                      key={`${item?.name}-${index}`}
                      tag={
                        <>
                          {item.permission === '0' ? (
                            <Tag className="rounded-[4px]" color="purple">
                              公开
                            </Tag>
                          ) : (
                            <Tag className="rounded-[4px]" color="orangered">
                              部分成员可访问
                            </Tag>
                          )}
                          {item.tags.map((tag, tagIndex) => (
                            <Tag
                              key={tagIndex}
                              className="rounded-[4px] bg-white text-[#5c5c5c] border-[#ebebeb]"
                              bordered
                            >
                              {tag}
                            </Tag>
                          ))}
                        </>
                      }
                      footerTag={
                        <Tag
                          className="rounded-[4px] transition-opacity duration-300"
                          color={item.status === '1' ? 'green' : 'red'}
                        >
                          {item.status === '1' ? '启用' : '禁用'}
                        </Tag>
                      }
                      switchChecked={item.status === '1'}
                      actionButtons={getActionButtons(item)}
                      description={item.description}
                      title={item.name}
                      createTime={formatDate(
                        item.update_time,
                        'YYYY-MM-DD HH:mm:ss'
                      )}
                      handleSwitch={(value) => handleSwitch(item, value)}
                      handleAction={(type) => handleAction(item, type)}
                      onClick={() => handleToDataset(item)}
                    />
                  );
                })
              ) : (
                <div className="absolute w-full h-full flex items-center justify-center bg-white">
                  <Space
                    direction="vertical"
                    size={16}
                    style={{ display: 'flex', alignItems: 'center' }}
                  >
                    <IconEmptyModelConfig style={{ width: 80, height: 80 }} />
                    <Text type="secondary">未找到匹配的知识库</Text>
                  </Space>
                </div>
              )}
            </div>
          </InfiniteScroll>
        </Spin>
      </Content>
      <KnowledgeModal ref={knowledgeModalRef} />
      <KnowledgeDeleteConfirmModal
        confirmDelete={confirmDelete}
        ref={knowledgeDeleteConfirmModalRef}
      />
    </>
  );
}
