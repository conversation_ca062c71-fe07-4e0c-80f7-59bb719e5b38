<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="0fe6f4de-1e92-4c33-9aee-b4ab1e7e4e82" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/src/pages/knowledge/components/aiStaff/info/components/settings/components/TreeModal/TreeModal.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/knowledge/components/aiStaff/info/components/settings/components/TreeModal/TreeModal.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/knowledge/components/aiStaff/info/components/settings/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/knowledge/components/aiStaff/info/components/settings/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/knowledge/components/knowledge/services/aiStaff-service.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/knowledge/components/knowledge/services/aiStaff-service.ts" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="30aFybk1aUmzExL6XBGxi4cvbqm" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ASKED_SHARE_PROJECT_CONFIGURATION_FILES&quot;: &quot;true&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;feature/AI-employees&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.stylelint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.stylelint&quot;: &quot;C:\\Users\\<USER>\\Desktop\\Ai4c\\agentfoundry-ui\\node_modules\\stylelint&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;pnpm&quot;,
    &quot;ts.external.directory.path&quot;: &quot;C:\\Users\\<USER>\\Desktop\\Ai4c\\agentfoundry-ui\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-WS-251.27812.50" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="0fe6f4de-1e92-4c33-9aee-b4ab1e7e4e82" name="更改" comment="" />
      <created>1753858032033</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753858032033</updated>
      <workItem from="1753858033399" duration="759000" />
      <workItem from="1753859111342" duration="99000" />
      <workItem from="1753859464430" duration="1237000" />
      <workItem from="1753861892171" duration="1484000" />
    </task>
    <task id="LOCAL-00001" summary="feat:修改文案，员工改为AI员工">
      <option name="closed" value="true" />
      <created>1753858437722</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1753858437722</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="feat:修改文案，员工改为AI员工" />
    <option name="LAST_COMMIT_MESSAGE" value="feat:修改文案，员工改为AI员工" />
  </component>
</project>